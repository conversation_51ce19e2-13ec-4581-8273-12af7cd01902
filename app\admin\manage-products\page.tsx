import { ProductsGridSkeleton } from "@/components/ProductSkeleton";
import { cache, Suspense } from "react";
import { ManageProductsClient } from "./manage-products-client";

// Cache manage products data fetching functions
const getManageProductsData = cache(async (categoryId?: string, sortBy?: string) => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;

    // Fetch categories
    const catRes = await fetch(`${baseUrl}/api/categories`, {
      next: { revalidate: 1800 }, // Cache for 30 minutes
      cache: 'force-cache'
    });
    const catData = catRes.ok ? await catRes.json() : { categories: [] };
    const categories = catData.categories || [];

    // Fetch products
    let productUrl = `${baseUrl}/api/products`;
    const params = new URLSearchParams();
    if (categoryId) {
      params.append("category", categoryId);
    }
    if (sortBy) {
      params.append("sort", sortBy);
    }
    if (params.toString()) {
      productUrl += `?${params.toString()}`;
    }

    const prodRes = await fetch(productUrl, {
      next: { revalidate: 1800 }, // Cache for 30 minutes
      cache: 'force-cache'
    });
    const prodData = prodRes.ok ? await prodRes.json() : { products: [] };
    const products = prodData.products || [];

    return { products, categories };
  } catch (error) {
    console.error("Error fetching manage products data:", error);
    return { products: [], categories: [] };
  }
})

export default async function Page({
  searchParams
}: {
  searchParams: Promise<{ category?: string; sort?: string }>
}) {
  // Await the searchParams Promise to access its properties
  const params = await searchParams;
  const selectedCategory = params.category;
  const selectedSort = params.sort;

  // Fetch initial data with caching
  const { products, categories } = await getManageProductsData(selectedCategory, selectedSort);

  return (
    <Suspense fallback={<ProductsGridSkeleton />}>
      <ManageProductsClient
        initialProducts={products}
        initialCategories={categories}
      />
    </Suspense>
  );
}